import { HttpService } from '@nestjs/axios';
import { Injectable, UnprocessableEntityException } from '@nestjs/common';
import {
  Address,
  CreateOrder,
  CreateSubAccountDTO,
  CreateWithdrawalDTO,
  CryptoMarket,
  MarketData,
  MarketSummaryResponse,
  MarketTickerResponse,
  RecentMarketTradeResponse,
  TempSwapQuotationBody,
  SubAccount,
  UpdateSubAccount,
  Wallet,
  InstantSwapBody,
} from './quidax.interface';
import { catchError, firstValueFrom } from 'rxjs';
import { AxiosError } from 'axios';

@Injectable()
export class QuidaxService {
  constructor(private readonly httpService: HttpService) {}

  async createSubAccount(body: CreateSubAccountDTO): Promise<SubAccount> {
    try {
      const { data } = await firstValueFrom(
        this.httpService.post('/users', body).pipe(
          catchError((error: AxiosError) => {
            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
      );
      return data.data;
    } catch (error) {
      if (error instanceof UnprocessableEntityException) {
        return await this.getSubaccounttByEmail(body.email);
      }
      throw error;
    }
  }

  async getSubAccountById(user_id: string): Promise<SubAccount> {
    const { data } = await firstValueFrom(
      this.httpService.get(`users/${user_id}`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as SubAccount;
  }

  async getSubaccounttByEmail(email: string): Promise<SubAccount> {
    const { data } = await firstValueFrom(
      this.httpService.get(`user/email?user_id=${email}`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as SubAccount;
  }

  async getSubAccounts(): Promise<SubAccount[]> {
    const { data } = await firstValueFrom(
      this.httpService.get('users').pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);
          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );

    return data.data as SubAccount[];
  }

  async getParentAccount(): Promise<SubAccount> {
    const { data } = await firstValueFrom(
      this.httpService.get(`users/me`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as SubAccount;
  }

  async updateSubAccount(
    user_id: string,
    body: UpdateSubAccount,
  ): Promise<SubAccount> {
    const { data } = await firstValueFrom(
      this.httpService.put(`users/${user_id}`, body).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as SubAccount;
  }

  async fetchUserWallets(user_id: string): Promise<Wallet[]> {
    const { data } = await firstValueFrom(
      this.httpService.get(`users/${user_id}/wallets`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as Wallet[];
  }

  async fetchWallet(user_id: string, currency: string): Promise<Wallet> {
    const { data } = await firstValueFrom(
      this.httpService.get(`users/${user_id}/wallets/${currency}`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as Wallet;
  }

  async fetchWalletPaymentAddress(
    user_id: string,
    currency: string,
  ): Promise<Address> {
    const { data } = await firstValueFrom(
      this.httpService.get(`users/${user_id}/wallets/${currency}/address`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as Address;
  }

  async fetchWalletPaymentAddresses(
    user_id: string,
    currency: string,
  ): Promise<Address[]> {
    const { data } = await firstValueFrom(
      this.httpService
        .get(`users/${user_id}/wallets/${currency}/addresses`)
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error?.response?.data);

            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );
    return data.data as Address[];
  }

  async fetchWalletAddressById(
    address_id: string,
    user_id: string,
    currency: string,
  ): Promise<Address> {
    const { data } = await firstValueFrom(
      this.httpService
        .get(`users/${user_id}/wallets/${currency}/addresses/${address_id}`)
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error?.response?.data);

            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );
    return data.data as Address;
  }

  async validateAddress(currency: string, address: string): Promise<any> {
    const { data } = await firstValueFrom(
      this.httpService.get(`${currency}/${address}/validate_address`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as any;
  }

  async createPaymentAddrressForACryptoCurrency(
    user_id: string,
    currency: string,
    network?: string,
  ): Promise<Address> {
    const url = network
      ? `users/${user_id}/wallets/${currency}/addresses?network=${network}`
      : `users/${user_id}/wallets/${currency}/addresses`;

    const { data } = await firstValueFrom(
      this.httpService.post(url).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as Address;
  }

  async getCryptoMarkets(): Promise<CryptoMarket[]> {
    const { data } = await firstValueFrom(
      this.httpService.get('markets').pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as CryptoMarket[];
  }

  async fetchMarketTickers(): Promise<MarketTickerResponse> {
    const { data } = await firstValueFrom(
      this.httpService.get('markets/tickers').pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data as MarketTickerResponse;
  }

  async fetchAMarketTicker(cryptoCurrency: string): Promise<MarketData> {
    const { data } = await firstValueFrom(
      this.httpService.get(`markets/tickers/${cryptoCurrency}`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as MarketData;
  }

  async getMarketSummary(): Promise<MarketSummaryResponse> {
    const { data } = await firstValueFrom(
      this.httpService.get(`markets/summary`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data as MarketSummaryResponse;
  }

  async transferCrypto(
    user_id: string,
    body: CreateWithdrawalDTO,
  ): Promise<any> {
    const { data } = await firstValueFrom(
      this.httpService.post(`users/${user_id}/withdraws`, body).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data as any;
  }

  async fetchWithdrawalsByReference(userId: string, reference: string) {
    const { data } = await firstValueFrom(
      this.httpService
        .get(`users/${userId}/withdraws/reference/${reference}`)
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error?.response?.data);

            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );
    return data.data as any;
  }

  async createOrder(userId: string, body: CreateOrder) {
    const { data } = await firstValueFrom(
      this.httpService.post(`users/${userId}/orders`, body).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data as any;
  }

  async fetchOrderDetails(userId: string, orderId: string) {
    const { data } = await firstValueFrom(
      this.httpService.get(`users/${userId}/orders/${orderId}`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as any;
  }

  async cancelOrder(userId: string, orderId: string) {
    const { data } = await firstValueFrom(
      this.httpService.post(`users/${userId}/orders/${orderId}/cancel`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as any;
  }

  async fetchRecentTradesForAMarket(marketPair: string) {
    const { data } = await firstValueFrom(
      this.httpService.get(`trades/${marketPair}`).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data as RecentMarketTradeResponse;
  }

  async temporarySwapQuotation(
    user_id: string,
    tempSwaps: TempSwapQuotationBody,
  ) {
    const { data } = await firstValueFrom(
      this.httpService
        .post(`users/${user_id}/temporary_swap_quotation`, tempSwaps)
        .pipe(
          catchError((error: AxiosError) => {
            console.log(error?.response?.data);

            throw new UnprocessableEntityException(error.response?.data);
          }),
        ),
    );
    return data.data as any;
  }

  async initiateInstantSwap(user_id: string, body: InstantSwapBody) {
    const { data } = await firstValueFrom(
      this.httpService.post(`users/${user_id}/swap_quotation`, body).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as any;
  }

  async confirmInstantSwap(user_id: string, body: InstantSwapBody) {
    const { data } = await firstValueFrom(
      this.httpService.post(`users/${user_id}/swap_quotation`, body).pipe(
        catchError((error: AxiosError) => {
          console.log(error?.response?.data);

          throw new UnprocessableEntityException(error.response?.data);
        }),
      ),
    );
    return data.data as any;
  }
}
