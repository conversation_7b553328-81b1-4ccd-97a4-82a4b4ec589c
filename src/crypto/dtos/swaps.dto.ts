import { ApiProperty } from '@nestjs/swagger';
import {
  IsBoolean,
  IsDateString,
  IsInt,
  IsOptional,
  IsString,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';

export class SwapDto {
  @IsString()
  id: string;

  @IsString()
  userId: string;

  @IsString()
  fromCurrency: string;

  @IsString()
  toCurrency: string;

  @IsString()
  fromAmount: string;

  @IsString()
  toAmount: string;

  @IsString()
  quotedPrice: string;

  @IsString()
  quotedCurrency: string;

  @IsString()
  quotedAmount: string;

  @IsString()
  expiresAt: string;

  @IsBoolean()
  confirmed: boolean;
}

export class GetSwapsByUserIdDto {
  @ApiProperty()
  @IsDateString()
  startDate: string;

  @ApiProperty()
  @IsDateString()
  endDate: string;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  page?: number = 1;

  @ApiProperty()
  @IsOptional()
  @Type(() => Number)
  @IsInt()
  @Min(1)
  limit?: number = 10;
}

export class createSwapDto {
  @ApiProperty()
  @IsString()
  fromCurrency: string;

  @ApiProperty()
  @IsString()
  toCurrency: string;

  @ApiProperty()
  @IsString()
  fromAmount: string;
}
