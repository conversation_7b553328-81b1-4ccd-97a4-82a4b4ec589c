import { Injectable, NotFoundException } from '@nestjs/common';
import { TypeOrmRepository } from '../../config/repository/typeorm.repository';
import { Swap } from '../entities/swaps.entity';
import { DataSource } from 'typeorm';
import { UserRepository } from './users.repository';
import { SwapDto } from '../dtos/swaps.dto';

@Injectable()
export class SwapsRepository extends TypeOrmRepository<Swap> {
  constructor(
    private readonly dataSource: DataSource,
    private readonly userRepository: UserRepository,
  ) {
    super(Swap, dataSource.createEntityManager());
  }

  async createSwap(swap: SwapDto): Promise<Swap> {
    const user = await this.userRepository.findOne({
      where: {
        userId: swap.userId,
      },
    });

    if (!user) {
      throw new NotFoundException('User not found');
    }

    const savedSwap = await this.save({
      ...swap,
      user,
    });

    return savedSwap;
  }

  async getSwap(swapId: string): Promise<Swap> {
    const swap = await this.findOne({
      where: {
        id: swapId,
      },
      relations: ['user'],
    });

    if (!swap) {
      throw new NotFoundException(`Swap with ID ${swapId} not found`);
    }

    return swap;
  }

  async getSwapsByUserId(
    userId: string,
    startDate: Date,
    endDate: Date,
    page: number = 1,
    limit: number = 10,
  ): Promise<{ swaps: Swap[]; total: number }> {
    const query = this.createQueryBuilder('swaps')
      .where('swaps.userId = :userId', { userId })
      .andWhere('swaps.createdAt >= :startDate', { startDate })
      .andWhere('swaps.createdAt <= :endDate', { endDate })
      .orderBy('swaps.createdAt', 'DESC')
      .skip((page - 1) * limit)
      .take(limit)
      .leftJoinAndSelect('swaps.user', 'user');

    const [swaps, total] = await query.getManyAndCount();

    return { swaps, total };
  }
}
