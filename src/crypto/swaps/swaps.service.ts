import { BadRequestException, Injectable } from '@nestjs/common';
import { SwapsRepository } from '../repositories/swaps.respository';
import { UserRepository } from '../repositories/users.repository';
import { QuidaxService } from '@app/quidax';
import { createSwapDto } from '../dtos/swaps.dto';
import { CustomerAuthData } from '@crednet/authmanager';
import { Swap } from '../entities/swaps.entity';

@Injectable()
export class SwapsService {
  constructor(
    private readonly swapsRepository: SwapsRepository,
    private readonly userRepository: UserRepository,
    private readonly quidaxService: QuidaxService,
  ) {}

  async createSwap(
    createSwapDto: createSwapDto,
    auth: CustomerAuthData,
  ): Promise<Swap> {
    const user = await this.userRepository.getUserByUserId(auth.id);
    if (!user) {
      throw new BadRequestException('User not found');
    }

    const response = await this.quidaxService.initiateInstantSwap(user.id, {
      from_currency: createSwapDto.fromCurrency,
      to_currency: createSwapDto.toCurrency,
      from_amount: createSwapDto.fromAmount,
    });

    return response;
  }
}
