import { Module } from '@nestjs/common';
import { SwapsService } from './swaps.service';
import { SwapsController } from './swaps.controller';
import { SwapsRepository } from '../repositories/swaps.respository';
import { UserRepository } from '../repositories/users.repository';

@Module({
  controllers: [SwapsController],
  providers: [SwapsService, SwapsRepository, UserRepository],
})
export class SwapsModule {}
